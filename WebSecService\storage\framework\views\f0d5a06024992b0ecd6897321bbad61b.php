<?php $__env->startSection('title', 'List Grades'); ?>
<?php $__env->startSection('content'); ?>
<div class="row my-4">
    <div class="col col-10">
        <h1>Grades</h1>
    </div>
    <div class="col col-2">
        <a href="<?php echo e(route('grades_edit')); ?>" class="btn btn-success form-control">Add Grade</a>
    </div>
</div>
<form>
    <div class="row mb-4">
        <div class="col col-sm-6">
            <input name="keywords" type="text"  class="form-control" placeholder="Search Keywords" value="<?php echo e(request()->keywords); ?>" />
        </div>
        <div class="col col-sm-2">
            <select name="order_by" class="form-select">
                <option value="" <?php echo e(request()->order_by==""?"selected":""); ?> disabled>Order By</option>
                <option value="courses.name" <?php echo e(request()->order_by=="courses.name"?"selected":""); ?>>Course Name</option>
                <option value="users.name" <?php echo e(request()->order_by=="users.name"?"selected":""); ?>>Student Name</option>
            </select>
        </div>
        <div class="col col-sm-2">
            <select name="order_direction" class="form-select">
                <option value="" <?php echo e(request()->order_direction==""?"selected":""); ?> disabled>Order Direction</option>
                <option value="ASC" <?php echo e(request()->order_direction=="ASC"?"selected":""); ?>>ASC</option>
                <option value="DESC" <?php echo e(request()->order_direction=="DESC"?"selected":""); ?>>DESC</option>
            </select>
        </div>
        <div class="col col-sm-1">
            <button type="submit" class="btn btn-primary">Submit</button>
        </div>
        <div class="col col-sm-1">
            <button type="reset" class="btn btn-danger">Reset</button>
        </div>
    </div>
</form>


<div class="card mt-2">
    <div class="card-body">
        <table class="table">
            <thead>
                <tr>
                    <th scope="col">Student</th>
                    <th scope="col">Course</th>
                    <th scope="col">Grade</th>
                    <th scope="col">Freezed</th>
                    <th scope="col"></th>
                </tr>
            </thead>
            <?php $__currentLoopData = $grades; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $grade): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <tr>
                <td scope="col"><?php echo e($grade->user->name); ?></td>
                <td scope="col"><?php echo e($grade->course->name); ?></td>
                <td scope="col"><?php echo e($grade->degree); ?> / <?php echo e($grade->course->max_degree); ?></td>
                <td scope="col">
                    <div class="row mb-2">
                        <div class="col col-4">
                            <a href="<?php echo e(route('grades_edit', $grade->id)); ?>" class="btn btn-success form-control">Edit</a>
                        </div>
                        <div class="col col-4">
                            <a href="<?php echo e(route('grades_delete', $grade->id)); ?>" class="btn btn-danger form-control">Delete</a>
                        </div>
                    </div>
                </td>
            </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </table>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\websec\WebSecService\resources\views/grades/list.blade.php ENDPATH**/ ?>