<?php

require 'vendor/autoload.php';

$app = require 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

try {
    echo "🔧 Creating missing tables: courses and grades\n\n";
    
    // Create courses table
    DB::statement("
        CREATE TABLE IF NOT EXISTS `courses` (
            `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            `code` varchar(32) NOT NULL,
            `name` varchar(256) NOT NULL,
            `max_degree` int(11) NOT NULL,
            `description` text DEFAULT NULL,
            `created_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`),
            UNIQUE KEY `courses_code_unique` (`code`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ Courses table created\n";
    
    // Create grades table
    DB::statement("
        CREATE TABLE IF NOT EXISTS `grades` (
            `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            `course_id` bigint(20) UNSIGNED NOT NULL,
            `user_id` bigint(20) UNSIGNED NOT NULL,
            `degree` double NOT NULL,
            `created_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`),
            KEY `grades_course_id_foreign` (`course_id`),
            KEY `grades_user_id_foreign` (`user_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ Grades table created\n";
    
    // Insert sample courses
    $courses = [
        ['id' => 1, 'code' => 'CRS112', 'name' => 'Math', 'max_degree' => 100, 'description' => 'Mathematics course'],
        ['id' => 2, 'code' => 'CRS113', 'name' => 'Physics', 'max_degree' => 100, 'description' => 'Physics course'],
        ['id' => 3, 'code' => 'CRS114', 'name' => 'Mechanics', 'max_degree' => 100, 'description' => 'Mechanics course'],
        ['id' => 4, 'code' => 'CRS115', 'name' => 'English', 'max_degree' => 100, 'description' => 'English course'],
        ['id' => 5, 'code' => 'CRS117', 'name' => 'Chemistry', 'max_degree' => 100, 'description' => 'Chemistry course'],
        ['id' => 6, 'code' => 'CET111', 'name' => 'Computer Programming', 'max_degree' => 200, 'description' => 'Computer Programming in Python'],
    ];
    
    foreach ($courses as $course) {
        DB::table('courses')->updateOrInsert(
            ['code' => $course['code']], 
            $course
        );
    }
    echo "✅ Sample courses inserted\n";
    
    // Insert sample grades
    $grades = [
        ['course_id' => 1, 'user_id' => 1, 'degree' => 90, 'created_at' => now(), 'updated_at' => now()],
        ['course_id' => 2, 'user_id' => 1, 'degree' => 85, 'created_at' => now(), 'updated_at' => now()],
        ['course_id' => 1, 'user_id' => 2, 'degree' => 78, 'created_at' => now(), 'updated_at' => now()],
        ['course_id' => 3, 'user_id' => 2, 'degree' => 92, 'created_at' => now(), 'updated_at' => now()],
    ];
    
    foreach ($grades as $grade) {
        // Check if user and course exist before inserting
        $userExists = DB::table('users')->where('id', $grade['user_id'])->exists();
        $courseExists = DB::table('courses')->where('id', $grade['course_id'])->exists();
        
        if ($userExists && $courseExists) {
            DB::table('grades')->updateOrInsert(
                ['course_id' => $grade['course_id'], 'user_id' => $grade['user_id']], 
                $grade
            );
        }
    }
    echo "✅ Sample grades inserted\n";
    
    // Final verification
    echo "\n📊 Final counts:\n";
    echo "📚 Courses: " . DB::table('courses')->count() . "\n";
    echo "📝 Grades: " . DB::table('grades')->count() . "\n";
    echo "👥 Users: " . DB::table('users')->count() . "\n";
    echo "🛍️ Products: " . DB::table('products')->count() . "\n";
    echo "🔐 Roles: " . DB::table('roles')->count() . "\n";
    echo "🔑 Permissions: " . DB::table('permissions')->count() . "\n";
    
    echo "\n🎉 All tables and data are now ready!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
