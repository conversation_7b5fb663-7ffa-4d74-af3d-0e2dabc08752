{"name": "nunomaduro/termwind", "description": "Its like Tailwind CSS, but for the console.", "keywords": ["php", "cli", "package", "console", "css", "style"], "license": "MIT", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.2", "ext-mbstring": "*", "symfony/console": "^7.2.6"}, "require-dev": {"illuminate/console": "^11.44.7", "laravel/pint": "^1.22.0", "mockery/mockery": "^1.6.12", "pestphp/pest": "^2.36.0 || ^3.8.2", "phpstan/phpstan": "^1.12.25", "phpstan/phpstan-strict-rules": "^1.6.2", "symfony/var-dumper": "^7.2.6", "thecodingmachine/phpstan-strict-rules": "^1.0.0"}, "autoload": {"psr-4": {"Termwind\\": "src/"}, "files": ["src/Functions.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "config": {"sort-packages": true, "preferred-install": "dist", "allow-plugins": {"pestphp/pest-plugin": true}}, "scripts": {"lint": "pint -v", "test:lint": "pint --test -v", "test:types": "phpstan analyse --ansi", "test:unit": "pest --colors=always", "test": ["@test:lint", "@test:types", "@test:unit"]}, "extra": {"laravel": {"providers": ["Termwind\\Laravel\\TermwindServiceProvider"]}, "branch-alias": {"dev-2.x": "2.x-dev"}}}