<?php $__env->startSection('title', 'Edit Course'); ?>
<?php $__env->startSection('content'); ?>

<form action="<?php echo e(route('courses_save', $course->id)); ?>" method="post">
    <?php echo e(csrf_field()); ?>

    <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <div class="alert alert-danger">
    <strong>Error!</strong> <?php echo e($error); ?>

    </div>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    <div class="row mb-2">
        <div class="col-6">
            <label for="code" class="form-label">Code:</label>
            <input type="text" class="form-control" placeholder="Code" name="code" required value="<?php echo e($course->code); ?>">
        </div>
        <div class="col-6">
            <label for="code" class="form-label">Max Degree:</label>
            <input type="text" class="form-control" placeholder="Max Degree" name="max_degree" required value="<?php echo e($course->max_degree); ?>">
        </div>
    </div>
    <div class="row mb-2">
        <div class="col">
            <label for="name" class="form-label">Name:</label>
            <input type="text" class="form-control" placeholder="Name" name="name" required value="<?php echo e($course->name); ?>">
        </div>
    </div>
    <div class="row mb-2">
        <div class="col">
            <label for="name" class="form-label">Description:</label>
            <textarea type="text" class="form-control" placeholder="Description" name="description" required><?php echo e($course->description); ?></textarea>
        </div>
    </div>
    <button type="submit" class="btn btn-primary">Submit</button>
</form>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\websec\WebSecService\resources\views/courses/edit.blade.php ENDPATH**/ ?>