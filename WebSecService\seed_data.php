<?php

require 'vendor/autoload.php';

$app = require 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

try {
    echo " Seeding database with sample data...\n\n";
    
    // 1. Insert courses
    DB::table('courses')->insert([
        ['id' => 1, 'code' => 'CRS112', 'name' => 'Math', 'max_degree' => 100, 'description' => null],
        ['id' => 2, 'code' => 'CRS113', 'name' => 'Physics', 'max_degree' => 100, 'description' => null],
        ['id' => 3, 'code' => 'CRS114', 'name' => 'Mechanics', 'max_degree' => 100, 'description' => null],
        ['id' => 4, 'code' => 'CRS115', 'name' => 'English', 'max_degree' => 100, 'description' => null],
        ['id' => 5, 'code' => 'CRS117', 'name' => 'Chemistry', 'max_degree' => 100, 'description' => null],
        ['id' => 7, 'code' => 'CET111', 'name' => 'Computer Programming', 'max_degree' => 200, 'description' => 'Computer Programming in Python'],
    ]);
    echo "Courses seeded\n";
    
    // 2. Insert roles
    DB::table('roles')->insert([
        ['id' => 1, 'name' => 'Admin', 'guard_name' => 'web', 'created_at' => null, 'updated_at' => null],
        ['id' => 2, 'name' => 'Employee', 'guard_name' => 'web', 'created_at' => null, 'updated_at' => null],
        ['id' => 3, 'name' => 'edumanager123', 'guard_name' => 'web', 'created_at' => null, 'updated_at' => null],
    ]);
    echo "Roles seeded\n";
    
    // 3. Insert permissions
    DB::table('permissions')->insert([
        ['id' => 1, 'name' => 'add_products', 'display_name' => 'Add Products', 'guard_name' => 'web', 'created_at' => null, 'updated_at' => null],
        ['id' => 2, 'name' => 'edit_products', 'display_name' => 'Edit Products', 'guard_name' => 'web', 'created_at' => null, 'updated_at' => null],
        ['id' => 3, 'name' => 'delete_products', 'display_name' => 'Delete Products', 'guard_name' => 'web', 'created_at' => null, 'updated_at' => null],
        ['id' => 4, 'name' => 'show_users', 'display_name' => 'Show Users', 'guard_name' => 'web', 'created_at' => null, 'updated_at' => null],
        ['id' => 5, 'name' => 'edit_users', 'display_name' => 'Edit Users', 'guard_name' => 'web', 'created_at' => null, 'updated_at' => null],
        ['id' => 7, 'name' => 'delete_users', 'display_name' => 'Delete Users', 'guard_name' => 'web', 'created_at' => null, 'updated_at' => null],
        ['id' => 8, 'name' => 'admin_users', 'display_name' => 'Admin Users', 'guard_name' => 'web', 'created_at' => null, 'updated_at' => null],
        ['id' => 9, 'name' => 'edit_courses', 'display_name' => 'Edit Courses', 'guard_name' => 'web', 'created_at' => null, 'updated_at' => null],
    ]);
    echo "Permissions seeded\n";
    
    // 4. Insert role_has_permissions
    DB::table('role_has_permissions')->insert([
        ['permission_id' => 1, 'role_id' => 1],
        ['permission_id' => 2, 'role_id' => 1],
        ['permission_id' => 3, 'role_id' => 1],
        ['permission_id' => 4, 'role_id' => 1],
        ['permission_id' => 4, 'role_id' => 2],
        ['permission_id' => 5, 'role_id' => 1],
        ['permission_id' => 5, 'role_id' => 2],
        ['permission_id' => 7, 'role_id' => 1],
        ['permission_id' => 8, 'role_id' => 1],
        ['permission_id' => 9, 'role_id' => 3],
    ]);
    echo " Role permissions seeded\n";
    
    // 5. Insert products
    DB::table('products')->insert([
        [
            'id' => 1, 'code' => 'TV01', 'name' => 'LG TV 50 Inch', 'price' => 28000, 'model' => 'LG8768787',
            'description' => 'High quality LG TV with 50 inch display',
            'photo' => 'lgtv50.jpg', 'created_at' => null, 'updated_at' => null, 'deleted_at' => null
        ],
        [
            'id' => 2, 'code' => 'RF01', 'name' => 'Toshiba Refrigerator 14"', 'price' => 22000, 'model' => 'TS76634',
            'description' => 'Energy efficient refrigerator with modern design',
            'photo' => 'tsrf50.jpg', 'created_at' => null, 'updated_at' => null, 'deleted_at' => null
        ],
        [
            'id' => 3, 'code' => 'RF02', 'name' => 'Toshiba Refrigerator 18"', 'price' => 28000, 'model' => 'TS76634',
            'description' => 'Large capacity refrigerator for families',
            'photo' => 'rf2.jpg', 'created_at' => null, 'updated_at' => null, 'deleted_at' => null
        ],
    ]);
    echo " Products seeded\n";
    
    // 6. Create admin user if not exists
    $adminUser = DB::table('users')->where('email', '<EMAIL>')->first();
    if (!$adminUser) {
        $userId = DB::table('users')->insertGetId([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password123'),
            'email_verified_at' => now(),
            'created_at' => now(),
            'updated_at' => now(),
        ]);
        
        // Assign admin role
        DB::table('model_has_roles')->insert([
            'role_id' => 1,
            'model_type' => 'App\\Models\\User',
            'model_id' => $userId,
        ]);
        echo "Admin user created\n";
    }
    
    echo "\n Database seeding completed successfully!\n\n";
    
    // Final verification
    $tables = ['courses', 'roles', 'permissions', 'products', 'users'];
    foreach ($tables as $table) {
        $count = DB::table($table)->count();
        echo " $table: $count records\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
