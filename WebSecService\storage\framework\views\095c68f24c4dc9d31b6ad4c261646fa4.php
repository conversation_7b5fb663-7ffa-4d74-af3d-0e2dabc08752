<?php $__env->startSection('title', 'Edit Grade'); ?>
<?php $__env->startSection('content'); ?>

<form action="<?php echo e(route('grades_save', $grade->id)); ?>" method="post">
    <?php echo e(csrf_field()); ?>

    <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <div class="alert alert-danger">
    <strong>Error!</strong> <?php echo e($error); ?>

    </div>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    <div class="row mb-2">
        <div class="col">
            <label for="code" class="form-label">Student:</label>
            <select name="user_id" class="form-control">
                <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option <?php echo e(($user->id==$grade->user_id)?"selected":""); ?> value="<?php echo e($user->id); ?>"><?php echo e($user->name); ?></option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
        </div>
    </div>
    <div class="row mb-2">
        <div class="col">
            <label for="code" class="form-label">Course:</label>
            <select name="course_id" class="form-control">
                <?php $__currentLoopData = $courses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option <?php echo e(($course->id==$grade->course_id)?"selected":""); ?> value="<?php echo e($course->id); ?>"><?php echo e($course->name); ?></option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
        </div>
    </div>
    <div class="row mb-2">
        <div class="col">
            <label for="name" class="form-label">Degree:</label>
            <input type="text" class="form-control" placeholder="Degree" name="degree" required value="<?php echo e($grade->degree); ?>">
        </div>
    </div>
    <button type="submit" class="btn btn-primary">Submit</button>
</form>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\websec\WebSecService\resources\views/grades/edit.blade.php ENDPATH**/ ?>