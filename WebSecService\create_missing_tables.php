<?php

require 'vendor/autoload.php';

$app = require 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

try {
    // Create missing tables
    
    // 1. Create courses table
    DB::statement("
        CREATE TABLE IF NOT EXISTS `courses` (
            `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            `code` varchar(32) NOT NULL,
            `name` varchar(256) NOT NULL,
            `max_degree` int(11) NOT NULL,
            `description` text DEFAULT NULL,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    // 2. Create grades table
    DB::statement("
        CREATE TABLE IF NOT EXISTS `grades` (
            `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            `course_id` bigint(20) UNSIGNED NOT NULL,
            `user_id` bigint(20) UNSIGNED NOT NULL,
            `degree` double NOT NULL,
            `created_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    // 3. Create products table
    DB::statement("
        CREATE TABLE IF NOT EXISTS `products` (
            `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            `code` varchar(64) NOT NULL,
            `name` varchar(256) NOT NULL,
            `price` int(10) UNSIGNED NOT NULL,
            `model` varchar(128) NOT NULL,
            `description` text DEFAULT NULL,
            `photo` varchar(128) DEFAULT NULL,
            `created_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp NULL DEFAULT NULL,
            `deleted_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    // 4. Create permissions table
    DB::statement("
        CREATE TABLE IF NOT EXISTS `permissions` (
            `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL,
            `display_name` varchar(128) DEFAULT NULL,
            `guard_name` varchar(255) NOT NULL,
            `created_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    // 5. Create roles table
    DB::statement("
        CREATE TABLE IF NOT EXISTS `roles` (
            `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL,
            `guard_name` varchar(255) NOT NULL,
            `created_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    // 6. Create model_has_roles table
    DB::statement("
        CREATE TABLE IF NOT EXISTS `model_has_roles` (
            `role_id` bigint(20) UNSIGNED NOT NULL,
            `model_type` varchar(255) NOT NULL,
            `model_id` bigint(20) UNSIGNED NOT NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    // 7. Create model_has_permissions table
    DB::statement("
        CREATE TABLE IF NOT EXISTS `model_has_permissions` (
            `permission_id` bigint(20) UNSIGNED NOT NULL,
            `model_type` varchar(255) NOT NULL,
            `model_id` bigint(20) UNSIGNED NOT NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    // 8. Create role_has_permissions table
    DB::statement("
        CREATE TABLE IF NOT EXISTS `role_has_permissions` (
            `permission_id` bigint(20) UNSIGNED NOT NULL,
            `role_id` bigint(20) UNSIGNED NOT NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    // 9. Create quizzes table
    DB::statement("
        CREATE TABLE IF NOT EXISTS `quizzes` (
            `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            `question` text NOT NULL,
            `instructor_id` bigint(20) UNSIGNED NOT NULL,
            `created_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    // 10. Create participations table
    DB::statement("
        CREATE TABLE IF NOT EXISTS `participations` (
            `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            `quiz_id` bigint(20) UNSIGNED NOT NULL,
            `student_id` bigint(20) UNSIGNED NOT NULL,
            `answer` text NOT NULL,
            `degree` int(11) NOT NULL,
            `created_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    echo "✅ All missing tables created successfully!\n";
    
    // Verify tables
    $tables = DB::select('SHOW TABLES');
    echo "\n📋 Total tables: " . count($tables) . "\n";
    foreach ($tables as $table) {
        $tableName = array_values((array)$table)[0];
        $count = DB::table($tableName)->count();
        echo "  - $tableName ($count records)\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
