<?php

require 'vendor/autoload.php';

$app = require 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

try {
    echo "🔍 Final Database Check\n";
    echo "======================\n\n";
    
    // Check database connection
    $pdo = DB::connection()->getPdo();
    echo "✅ Database connection: SUCCESS\n";
    
    // Get current database name
    $dbName = DB::connection()->getDatabaseName();
    echo "📊 Current database: $dbName\n\n";
    
    // Check if we're using the right database
    if ($dbName !== 'websec') {
        echo "⚠️  Warning: Using database '$dbName' instead of 'websec'\n";
        echo "🔄 Switching to websec database...\n";
        DB::statement("USE websec");
        echo "✅ Switched to websec database\n\n";
    }
    
    // List all tables
    $tables = DB::select('SHOW TABLES');
    echo "📋 Total tables: " . count($tables) . "\n\n";
    
    $requiredTables = [
        'users', 'courses', 'grades', 'products', 'permissions', 
        'roles', 'model_has_roles', 'role_has_permissions'
    ];
    
    $existingTables = [];
    foreach ($tables as $table) {
        $tableName = array_values((array)$table)[0];
        $existingTables[] = $tableName;
        $count = DB::table($tableName)->count();
        $status = in_array($tableName, $requiredTables) ? "✅" : "ℹ️";
        echo "$status $tableName ($count records)\n";
    }
    
    echo "\n🔍 Missing Required Tables:\n";
    $missingTables = array_diff($requiredTables, $existingTables);
    if (empty($missingTables)) {
        echo "✅ All required tables exist!\n";
    } else {
        foreach ($missingTables as $table) {
            echo "❌ $table\n";
        }
    }
    
    // Check specific data
    echo "\n📊 Data Summary:\n";
    if (in_array('users', $existingTables)) {
        $userCount = DB::table('users')->count();
        echo "👥 Users: $userCount\n";
    }
    
    if (in_array('courses', $existingTables)) {
        $courseCount = DB::table('courses')->count();
        echo "📚 Courses: $courseCount\n";
    }
    
    if (in_array('products', $existingTables)) {
        $productCount = DB::table('products')->count();
        echo "🛍️  Products: $productCount\n";
    }
    
    if (in_array('roles', $existingTables)) {
        $roleCount = DB::table('roles')->count();
        echo "🔐 Roles: $roleCount\n";
    }
    
    if (in_array('permissions', $existingTables)) {
        $permissionCount = DB::table('permissions')->count();
        echo "🔑 Permissions: $permissionCount\n";
    }
    
    echo "\n🎯 System Status:\n";
    
    // Check Laravel key
    $appKey = env('APP_KEY');
    if ($appKey) {
        echo "✅ APP_KEY: Configured\n";
    } else {
        echo "❌ APP_KEY: Missing\n";
    }
    
    // Check database config
    $dbConfig = config('database.connections.mysql');
    echo "✅ Database Host: " . $dbConfig['host'] . "\n";
    echo "✅ Database Name: " . $dbConfig['database'] . "\n";
    echo "✅ Database User: " . $dbConfig['username'] . "\n";
    
    echo "\n🏁 Database setup is ready for use!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
