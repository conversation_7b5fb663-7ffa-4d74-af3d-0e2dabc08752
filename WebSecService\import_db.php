<?php

try {
    // Create database connection
    $pdo = new PDO('mysql:host=127.0.0.1;port=3306', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Create database if not exists
    $pdo->exec("CREATE DATABASE IF NOT EXISTS websec CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✅ Database 'websec' created/verified successfully!\n";
    
    // Switch to the database
    $pdo->exec("USE websec");
    
    // Read and execute SQL file
    $sqlFile = 'database/websec.sql';
    if (file_exists($sqlFile)) {
        $sql = file_get_contents($sqlFile);
        
        // Split SQL into individual statements
        $statements = array_filter(array_map('trim', explode(';', $sql)));
        
        $successCount = 0;
        $errorCount = 0;
        
        foreach ($statements as $statement) {
            if (!empty($statement) && !preg_match('/^(--|\/\*|\*|SET|START|COMMIT)/', $statement)) {
                try {
                    $pdo->exec($statement);
                    $successCount++;
                } catch (PDOException $e) {
                    // Skip errors for existing tables/data
                    if (strpos($e->getMessage(), 'already exists') === false && 
                        strpos($e->getMessage(), 'Duplicate entry') === false) {
                        echo "⚠️  Warning: " . $e->getMessage() . "\n";
                        $errorCount++;
                    }
                }
            }
        }
        
        echo "✅ SQL import completed!\n";
        echo "📊 Statements executed: $successCount\n";
        if ($errorCount > 0) {
            echo "⚠️  Warnings: $errorCount\n";
        }
        
        // Verify tables
        $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
        echo "\n📋 Tables in database (" . count($tables) . "):\n";
        foreach ($tables as $table) {
            $count = $pdo->query("SELECT COUNT(*) FROM `$table`")->fetchColumn();
            echo "  - $table ($count records)\n";
        }
        
    } else {
        echo "❌ SQL file not found: $sqlFile\n";
    }
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "❌ General error: " . $e->getMessage() . "\n";
}
