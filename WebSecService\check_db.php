<?php

require 'vendor/autoload.php';

$app = require 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

try {
    // Test database connection
    $pdo = DB::connection()->getPdo();
    echo " Database connection successful!\n";
    
    // Check users table
    $usersCount = App\Models\User::count();
    echo "👥 Users count: $usersCount\n";
    
    // Check if we have any tables
    $tables = DB::select('SHOW TABLES');
    echo " Total tables: " . count($tables) . "\n";
    
    echo "\n Tables in database:\n";
    foreach ($tables as $table) {
        $tableName = array_values((array)$table)[0];
        echo "  - $tableName\n";
    }
    
} catch (Exception $e) {
    echo " Error: " . $e->getMessage() . "\n";
}
